# Git repository
.git
.gitignore
.gitattributes

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Environment files (keep .env.production as it's needed in Dockerfile)
.env
.env.development
.env.local
.env.test

# Build artifacts and binaries
*.exe
*.exe~
*.dll
*.so
*.dylib
server
cmd/server/server

# Test files and coverage
*.test
*.out
coverage.txt
coverage.html

# Temporary files
*.tmp
*.temp
*.log
*.pid

# Documentation
README.md
*.md
docs/
documentation/

# Docker files (not needed in build context)
Dockerfile*
docker-compose*.yml
.dockerignore

# Development tools and configs
Makefile
.github/
.gitmodules

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (if any frontend assets)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Go workspace files
go.work
go.work.sum

# Vendor directory (if using vendoring)
vendor/

# Any local development scripts
scripts/dev/
scripts/local/
