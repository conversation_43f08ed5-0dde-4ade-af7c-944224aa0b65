package main

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"

	"exert-app-service/cmd/server/testutil"
	"exert-app-service/internal/program"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestWorkoutProgram(t *testing.T) {
	ts := testutil.SetupTest(t)
	defer ts.Pool.Close()

	var testProgram1 program.WorkoutProgramDao
	var testProgram2 program.WorkoutProgramDao

	t.Run("Create Program", func(t *testing.T) {
		testProgram1 = testutil.CreateTestProgram(t, ts.App, ts.AuthToken.AccessToken)
	})

	t.Run("Get All Programs", func(t *testing.T) {
		// Create a 2nd program for this test
		testProgram2 = testutil.CreateTestProgram(t, ts.App, ts.AuthToken.AccessToken)

		req := httptest.NewRequest("GET", "/v1/api/program", nil)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+ts.AuthToken.AccessToken)

		resp, err := ts.App.Test(req)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var programs []program.WorkoutProgramDao
		err = json.NewDecoder(resp.Body).Decode(&programs)
		require.NoError(t, err)
		assert.Len(t, programs, 2)
		receivedProgram1 := programs[0]
		receivedProgram2 := programs[1]
		assert.Equal(t, testProgram1.ID, receivedProgram1.ID)
		assert.Equal(t, testProgram1.Name, receivedProgram1.Name)
		assert.Equal(t, testProgram2.ID, receivedProgram2.ID)
		assert.Equal(t, testProgram2.Name, receivedProgram2.Name)
	})

	t.Run("Get Single Program", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/v1/api/program/"+strconv.Itoa(int(testProgram1.ID)), nil)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+ts.AuthToken.AccessToken)

		resp, err := ts.App.Test(req)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var retrievedProgram program.WorkoutProgramDao
		err = json.NewDecoder(resp.Body).Decode(&retrievedProgram)
		require.NoError(t, err)
		assert.Equal(t, testProgram1, retrievedProgram)
	})

	t.Run("Update Program", func(t *testing.T) {
		// Create a new program for this test
		testProgram1 = testutil.CreateTestProgram(t, ts.App, ts.AuthToken.AccessToken)

		updatedProgram := testProgram1
		updatedProgram.Name = "Updated Program"
		updatedProgram.Description = "An updated test workout program"
		updatedProgram.ProgramRoutines[0].Name = "Updated Warm Up"

		jsonData, _ := json.Marshal(updatedProgram)
		req := httptest.NewRequest("PUT", "/v1/api/program", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+ts.AuthToken.AccessToken)

		resp, err := ts.App.Test(req)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var result program.WorkoutProgramDao
		err = json.NewDecoder(resp.Body).Decode(&result)
		require.NoError(t, err)
		assert.Equal(t, updatedProgram.Name, result.Name)
		assert.Equal(t, updatedProgram.Description, result.Description)
		assert.Equal(t, updatedProgram.ProgramRoutines[0].Name, result.ProgramRoutines[0].Name)
	})

	t.Run("Delete Program", func(t *testing.T) {
		// Create a new program for this test
		testProgram1 = testutil.CreateTestProgram(t, ts.App, ts.AuthToken.AccessToken)

		req := httptest.NewRequest("DELETE", "/v1/api/program/"+strconv.Itoa(int(testProgram1.ID)), nil)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+ts.AuthToken.AccessToken)

		resp, err := ts.App.Test(req)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode)

		// Verify the program was deleted by trying to fetch it
		req = httptest.NewRequest("GET", "/v1/api/program/"+strconv.Itoa(int(testProgram1.ID)), nil)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+ts.AuthToken.AccessToken)

		resp, err = ts.App.Test(req)
		require.NoError(t, err)
		assert.Equal(t, http.StatusNotFound, resp.StatusCode)
	})
}
