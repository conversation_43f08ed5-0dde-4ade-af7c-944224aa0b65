package testutil

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"exert-app-service/internal/program"

	"github.com/gofiber/fiber/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func CreateTestProgram(t *testing.T, app *fiber.App, authToken string) program.WorkoutProgramDao {
	testProgram := program.WorkoutProgramDao{
		ID:          0,
		Name:        "Test Program",
		Description: "A test workout program",
		ProgramRoutines: []program.ProgramRoutineDao{
			{
				Name:        "Warm Up",
				Description: "Warm up routine",
				Order:       1,
				Groups: []program.ProgramGroupDao{
					{
						Name:  "Light Jog",
						Sets:  1,
						Reps:  1,
						Order: 1,
					},
				},
			},
		},
	}

	jsonData, _ := json.Marshal(testProgram)
	req := httptest.NewRequest("POST", "/v1/api/program", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+authToken)

	resp, err := app.Test(req)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var createdProgram program.WorkoutProgramDao
	err = json.NewDecoder(resp.Body).Decode(&createdProgram)
	require.NoError(t, err)
	assert.NotZero(t, createdProgram.ID)

	return createdProgram
}
