package testutil

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"exert-app-service/internal/session"

	"github.com/gofiber/fiber/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func CreateTestSession(t *testing.T, router *fiber.App, authToken string) session.WorkoutSessionDao {
	now := time.Now().Format(time.RFC3339)
	testSession := session.WorkoutSessionDao{
		Name:        "Test Session",
		Description: "A test workout session",
		StartTime:   now,
		EndTime:     now,
		SessionGroups: []session.SessionGroupDao{
			{
				Name:        "Warm Up",
				Description: "Warm up group",
				Order:       1,
				Sets: []session.SessionSetDao{
					{
						Reps:   10,
						Weight: 0,
						Order:  1,
					},
				},
			},
		},
	}

	jsonData, _ := json.Marshal(testSession)
	req := httptest.NewRequest("POST", "/v1/api/session", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+authToken)

	resp, err := router.Test(req)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var createdSession session.WorkoutSessionDao
	err = json.NewDecoder(resp.Body).Decode(&createdSession)
	require.NoError(t, err)
	assert.NotZero(t, createdSession.ID)

	return createdSession
}
