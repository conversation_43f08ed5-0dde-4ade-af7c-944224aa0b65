package testutil

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"exert-app-service/internal/auth"

	"github.com/gofiber/fiber/v2"
	"github.com/stretchr/testify/assert"
)

func RegisterUser(t *testing.T, app *fiber.App, username, email, password string) {
	registerData := map[string]string{
		"username": username,
		"email":    email,
		"password": password,
	}
	jsonData, _ := json.Marshal(registerData)

	req := httptest.NewRequest("POST", "/v1/api/auth/register", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	resp, err := app.Test(req)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusCreated, resp.StatusCode)
}

func LoginUser(t *testing.T, app *fiber.App, username, password string) auth.TokenResponse {
	loginData := map[string]string{
		"username": username,
		"password": password,
	}
	jsonData, _ := json.Marshal(loginData)

	req := httptest.NewRequest("POST", "/v1/api/auth/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	resp, err := app.Test(req)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var tokenResponse auth.TokenResponse
	err = json.NewDecoder(resp.Body).Decode(&tokenResponse)
	assert.NoError(t, err)

	return tokenResponse
}
