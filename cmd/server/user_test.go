package main

import (
	"testing"

	"exert-app-service/cmd/server/testutil"

	"github.com/stretchr/testify/assert"
)

func TestUserAuthentication(t *testing.T) {
	ts := testutil.SetupTest(t)
	defer ts.Pool.Close()

	t.Run("Register User", func(t *testing.T) {
		testutil.RegisterUser(t, ts.App, "newuser", "<EMAIL>", "newpassword")
	})

	t.Run("Login User", func(t *testing.T) {
		testutil.RegisterUser(t, ts.App, "loginuser", "<EMAIL>", "loginpassword")
		tokenResponse := testutil.LoginUser(t, ts.App, "loginuser", "loginpassword")

		assert.NotNil(t, tokenResponse.ExpiresAt)
		assert.NotEmpty(t, tokenResponse.AccessToken)
		assert.NotEmpty(t, tokenResponse.RefreshToken)
	})
}
