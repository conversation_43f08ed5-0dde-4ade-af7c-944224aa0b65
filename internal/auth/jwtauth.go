package auth

import (
	"exert-app-service/internal/util"
	"strings"

	"github.com/gofiber/fiber/v2"
)

type contextKey string

const UserIDKey contextKey = "user_id"

// JWTAuthMiddleware middleware for authenticating JWT tokens
func JWTAuthMiddleware(jwtService *JWTService) func(fiber.Handler) fiber.Handler {
	return func(next fiber.Handler) fiber.Handler {
		return func(c *fiber.Ctx) error {
			authHeader := c.Get("Authorization")

			// Check if Authorization header exists
			if authHeader == "" {
				util.RespondWithError(c, fiber.StatusUnauthorized, "Authorization header is required")
				return c.Next()
			}

			// Check if the format is correct
			parts := strings.Split(authHeader, " ")
			if len(parts) != 2 || parts[0] != "Bearer" {
				util.RespondWithError(c, fiber.StatusUnauthorized, "Authorization header format must be Bearer {token}")
				return c.Next()
			}

			// Validate token
			tokenString := parts[1]
			claims, err := jwtService.ValidateAccessToken(tokenString)
			if err != nil {
				util.RespondWithError(c, fiber.StatusUnauthorized, "Invalid or expired token")
				return c.Next()
			}

			// Add user ID to context
			c.Locals(UserIDKey, claims.UserID)

			// Call the next handler with the updated context
			return next(c)
		}
	}
}

// GetUserID gets the user ID from the request context
func GetUserID(c *fiber.Ctx) (int64, bool) {
	userID, ok := c.Context().Value(UserIDKey).(int64)
	return userID, ok
}
