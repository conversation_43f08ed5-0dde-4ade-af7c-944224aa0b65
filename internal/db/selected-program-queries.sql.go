// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.28.0
// source: selected-program-queries.sql

package db

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createSelectedProgram = `-- name: CreateSelectedProgram :one
insert into user_program_selections (user_id, program_id)
values ($1, $2)
returning id, user_id, program_id
`

type CreateSelectedProgramParams struct {
	UserID    pgtype.Int4
	ProgramID pgtype.Int4
}

type CreateSelectedProgramRow struct {
	ID        int32
	UserID    pgtype.Int4
	ProgramID pgtype.Int4
}

func (q *Queries) CreateSelectedProgram(ctx context.Context, arg CreateSelectedProgramParams) (CreateSelectedProgramRow, error) {
	row := q.db.QueryRow(ctx, createSelectedProgram, arg.UserID, arg.ProgramID)
	var i CreateSelectedProgramRow
	err := row.Scan(&i.ID, &i.UserID, &i.ProgramID)
	return i, err
}

const deleteSelectedProgram = `-- name: DeleteSelectedProgram :exec
delete from user_program_selections
where id = $1
`

func (q *Queries) DeleteSelectedProgram(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, deleteSelectedProgram, id)
	return err
}

const getSelectedProgram = `-- name: GetSelectedProgram :many
select sp.id,
       sp.program_id,
       sp.user_id
from user_program_selections as sp
where sp.user_id = $1
`

type GetSelectedProgramRow struct {
	ID        int32
	ProgramID pgtype.Int4
	UserID    pgtype.Int4
}

func (q *Queries) GetSelectedProgram(ctx context.Context, userID pgtype.Int4) ([]GetSelectedProgramRow, error) {
	rows, err := q.db.Query(ctx, getSelectedProgram, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetSelectedProgramRow
	for rows.Next() {
		var i GetSelectedProgramRow
		if err := rows.Scan(&i.ID, &i.ProgramID, &i.UserID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
