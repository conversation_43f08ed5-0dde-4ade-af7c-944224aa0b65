package program

import (
	"exert-app-service/internal/auth"
	"fmt"
	"log"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type ProgramHandler struct {
	Ps *ProgramService
}

func NewProgramHandler(ps *ProgramService) *ProgramHandler {
	return &ProgramHandler{
		Ps: ps,
	}
}

func (h *ProgramHandler) ListAll(c *fiber.Ctx) error {
	userID, ok := auth.GetUserID(c)
	if !ok {
		log.Println("user not authenticated: ", userID)
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User not authenticated",
		})
	}

	h.Ps.SetUserID(userID)
	programs, err := h.Ps.GetAll()
	if err != nil {
		log.Println("error getting workout programs: ", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get workout programs",
		})
	}

	return c.Status(fiber.StatusOK).JSON(programs)
}

func (h *ProgramHandler) List(c *fiber.Ctx) error {
	userID, ok := auth.GetUserID(c)
	if !ok {
		log.Println("user not authenticated: ", userID)
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User not authenticated",
		})
	}

	h.Ps.SetUserID(userID)
	id, err := h.getProgramId(c)
	if err != nil {
		log.Println("error getting program id: ", err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Failed to get program ID",
		})
	}

	program, err := h.Ps.Get(id)
	if err != nil || program.ID == 0 {
		log.Println("program not found: ", err)
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Program not found",
		})
	}

	return c.Status(fiber.StatusOK).JSON(program)
}

func (h *ProgramHandler) Create(c *fiber.Ctx) error {
	userID, ok := auth.GetUserID(c)
	if !ok {
		log.Println("user not authenticated: ", userID)
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User not authenticated",
		})
	}

	h.Ps.SetUserID(userID)
	var newProgram WorkoutProgramDao
	if err := c.BodyParser(&newProgram); err != nil {
		log.Println("error decoding program: ", err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid program",
		})
	}

	createdProgram, err := h.Ps.Create(newProgram)
	if err != nil {
		log.Println("error creating workout program: ", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create workout program",
		})
	}

	return c.Status(fiber.StatusOK).JSON(createdProgram)
}

func (h *ProgramHandler) Update(c *fiber.Ctx) error {
	userID, ok := auth.GetUserID(c)
	if !ok {
		log.Println("user not authenticated: ", userID)
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User not authenticated",
		})
	}

	h.Ps.SetUserID(userID)
	var newProgram WorkoutProgramDao
	if err := c.BodyParser(&newProgram); err != nil {
		log.Println("error decoding program: ", err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid program",
		})
	}

	createdProgram, err := h.Ps.Update(newProgram)
	if err != nil {
		log.Println("error updating workout program: ", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create workout program",
		})
	}

	return c.Status(fiber.StatusOK).JSON(createdProgram)
}

func (h *ProgramHandler) Delete(c *fiber.Ctx) error {
	userID, ok := auth.GetUserID(c)
	if !ok {
		log.Println("user not authenticated: ", userID)
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User not authenticated",
		})
	}

	h.Ps.SetUserID(userID)
	id, err := h.getProgramId(c)
	if err != nil {
		log.Println("error getting program id: ", err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Failed to get program ID",
		})
	}

	err = h.Ps.Delete(id)
	if err != nil {
		log.Println("error deleting workout program: ", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to delete workout program",
		})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Workout program deleted",
	})
}

func (h *ProgramHandler) getProgramId(c *fiber.Ctx) (int32, error) {
	id := c.Params("programId")
	if id == "" {
		log.Println("no program ID provided")
		return 0, fmt.Errorf("no program ID provided")
	}

	programId, err := strconv.Atoi(id)
	if err != nil {
		log.Println("failed to parse program ID: ", err)
		return 0, fmt.Errorf("failed to parse program ID")
	}

	return int32(programId), nil
}
