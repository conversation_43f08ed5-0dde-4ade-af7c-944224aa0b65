package program

import (
	"exert-app-service/internal/db"
	"sort"
)

type WorkoutProgramDao struct {
	ID              int32               `json:"id"`
	Name            string              `json:"name"`
	Description     string              `json:"description"`
	ProgramRoutines []ProgramRoutineDao `json:"routines"`
}

type ProgramRoutineDao struct {
	ID          int32             `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Order       int32             `json:"order"`
	Groups      []ProgramGroupDao `json:"groups"`
	ProgramID   int32             `json:"programId"`
}

type ProgramGroupDao struct {
	ID        int32  `json:"id"`
	Name      string `json:"name"`
	Sets      int32  `json:"sets"`
	Reps      int32  `json:"reps"`
	Order     int32  `json:"order"`
	RoutineID int32  `json:"routineId"`
}

type GroupById []ProgramGroupDao

func (a GroupById) Len() int           { return len(a) }
func (a GroupById) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a GroupById) Less(i, j int) bool { return a[i].ID < a[j].ID }

type RoutineById []ProgramRoutineDao

func (a RoutineById) Len() int           { return len(a) }
func (a RoutineById) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a RoutineById) Less(i, j int) bool { return a[i].ID < a[j].ID }

type ProgramById []WorkoutProgramDao

func (a ProgramById) Len() int           { return len(a) }
func (a ProgramById) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a ProgramById) Less(i, j int) bool { return a[i].ID < a[j].ID }

func MapToWorkoutProgram(rows []db.GetWorkoutProgramRow) WorkoutProgramDao {
	if len(rows) == 0 {
		return WorkoutProgramDao{}
	}

	program := WorkoutProgramDao{
		ID:              rows[0].ID,
		Name:            rows[0].Name,
		Description:     rows[0].Description.String,
		ProgramRoutines: make([]ProgramRoutineDao, 0),
	}

	routineMap := make(map[int32]*ProgramRoutineDao)

	for _, row := range rows {
		routine, ok := routineMap[row.RoutineID]
		if !ok {
			routine = &ProgramRoutineDao{
				ID:          row.RoutineID,
				Name:        row.RoutineName,
				Description: row.RoutineDescription.String,
				Order:       row.RoutineOrder,
				Groups:      make([]ProgramGroupDao, 0),
				ProgramID:   row.RoutineProgramID,
			}
			routineMap[row.RoutineID] = routine
		}

		group := ProgramGroupDao{
			ID:        row.GroupID,
			Name:      row.GroupName,
			Sets:      row.Sets,
			Reps:      row.Reps,
			Order:     row.GroupOrder,
			RoutineID: row.GroupRoutineID,
		}

		routine.Groups = append(routine.Groups, group)
	}

	for _, routine := range routineMap {
		sort.Sort(GroupById(routine.Groups))
		program.ProgramRoutines = append(program.ProgramRoutines, *routine)
	}
	sort.Sort(RoutineById(program.ProgramRoutines))
	return program
}

func MapToWorkoutPrograms(rows []db.GetWorkoutProgramsRow) []WorkoutProgramDao {
	if len(rows) == 0 {
		return []WorkoutProgramDao{}
	}

	programRows := make(map[int32][]db.GetWorkoutProgramsRow)
	for _, row := range rows {
		programRows[row.ID] = append(programRows[row.ID], row)
	}

	programs := make([]WorkoutProgramDao, 0, len(programRows))
	for _, rowsForProgram := range programRows {
		program := MapToWorkoutProgram(convertProgramRows(rowsForProgram))
		programs = append(programs, program)
	}

	sort.Sort(ProgramById(programs))
	return programs
}

func convertProgramRows(rows []db.GetWorkoutProgramsRow) []db.GetWorkoutProgramRow {
	converted := make([]db.GetWorkoutProgramRow, 0, len(rows))
	for _, row := range rows {
		converted = append(converted, db.GetWorkoutProgramRow(row))
	}
	return converted
}
