package program

import (
	"context"
	"exert-app-service/internal/db"
	"fmt"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type ProgramService struct {
	Ctx    context.Context
	Pool   *pgxpool.Pool
	UserID int64
	Wps    *WorkoutProgramService
	Prs    *ProgramRoutineService
	Pgs    *ProgramGroupService
}

func NewProgramService(ctx context.Context, pool *pgxpool.Pool, wps *WorkoutProgramService, prs *ProgramRoutineService, pgs *ProgramGroupService) *ProgramService {
	return &ProgramService{
		Ctx:  ctx,
		Pool: pool,
		Wps:  wps,
		Prs:  prs,
		Pgs:  pgs,
	}
}

func (s *ProgramService) SetUserID(userID int64) {
	s.UserID = userID
	s.Wps.UserID = userID
	s.Prs.UserID = userID
	s.Pgs.UserID = userID
}

func (s *ProgramService) GetAll() ([]WorkoutProgramDao, error) {
	q := db.New(s.Pool)
	programs, err := s.Wps.GetAll(q)
	if err != nil {
		return nil, fmt.Errorf("failed to get all programs: %w", err)
	}

	return MapToWorkoutPrograms(programs), nil
}

func (s *ProgramService) Get(id int32) (WorkoutProgramDao, error) {
	q := db.New(s.Pool)
	program, err := s.Wps.GetById(q, id)
	if err != nil {
		return WorkoutProgramDao{}, fmt.Errorf("failed to get program: %w", err)
	}

	return MapToWorkoutProgram(program), nil
}

func (s *ProgramService) Update(programDao WorkoutProgramDao) (WorkoutProgramDao, error) {
	tx, err := s.Pool.BeginTx(s.Ctx, pgx.TxOptions{})
	if err != nil {
		return WorkoutProgramDao{}, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(s.Ctx)

	q := db.New(tx)
	program, err := s.Wps.Upsert(q, programDao)
	if err != nil {
		return WorkoutProgramDao{}, fmt.Errorf("failed to upsert program: %w", err)
	}
	programDao.ID = program.ID

	for i, routine := range programDao.ProgramRoutines {
		updatedRoutine, err := s.Prs.Upsert(q, routine, programDao.ID)
		if err != nil {
			return WorkoutProgramDao{}, fmt.Errorf("failed to upsert routine: %w", err)
		}
		programDao.ProgramRoutines[i].ID = updatedRoutine.ID

		for j, group := range routine.Groups {
			updatedGroup, err := s.Pgs.Upsert(q, group, updatedRoutine.ID)
			if err != nil {
				return WorkoutProgramDao{}, fmt.Errorf("failed to upsert group: %w", err)
			}
			programDao.ProgramRoutines[i].Groups[j].ID = updatedGroup.ID
		}

		if err := s.Pgs.DeleteNotInSlice(q, routine.ID, getProgramGroupIDs(routine.Groups)); err != nil {
			return WorkoutProgramDao{}, fmt.Errorf("failed to delete groups: %w", err)
		}
	}

	if err := s.Prs.DeleteNotInSlice(q, programDao.ID, getProgramRoutineIDs(programDao.ProgramRoutines)); err != nil {
		return WorkoutProgramDao{}, fmt.Errorf("failed to delete routines: %w", err)
	}

	if err := tx.Commit(s.Ctx); err != nil {
		return WorkoutProgramDao{}, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return programDao, nil
}

func (s *ProgramService) Create(programDao WorkoutProgramDao) (WorkoutProgramDao, error) {
	tx, err := s.Pool.BeginTx(s.Ctx, pgx.TxOptions{})
	if err != nil {
		return WorkoutProgramDao{}, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(s.Ctx)

	q := db.New(tx)
	program, err := s.Wps.Upsert(q, programDao)
	if err != nil {
		return WorkoutProgramDao{}, fmt.Errorf("failed to upsert program: %w", err)
	}
	programDao.ID = program.ID

	for i, routine := range programDao.ProgramRoutines {
		updatedRoutine, err := s.Prs.Upsert(q, routine, programDao.ID)
		if err != nil {
			return WorkoutProgramDao{}, fmt.Errorf("failed to upsert routine: %w", err)
		}

		programDao.ProgramRoutines[i].ID = updatedRoutine.ID
		programDao.ProgramRoutines[i].ProgramID = updatedRoutine.ProgramID

		for j, group := range routine.Groups {
			updatedGroup, err := s.Pgs.Upsert(q, group, updatedRoutine.ID)
			if err != nil {
				return WorkoutProgramDao{}, fmt.Errorf("failed to upsert group: %w", err)
			}
			programDao.ProgramRoutines[i].Groups[j].ID = updatedGroup.ID
			programDao.ProgramRoutines[i].Groups[j].RoutineID = updatedGroup.RoutineID
		}
	}

	if err := tx.Commit(s.Ctx); err != nil {
		return WorkoutProgramDao{}, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return programDao, nil
}

func (s *ProgramService) Delete(id int32) error {
	q := db.New(s.Pool)
	return s.Wps.Delete(q, id)
}

func getProgramRoutineIDs(routines []ProgramRoutineDao) []int32 {
	ids := make([]int32, len(routines))
	for i, r := range routines {
		ids[i] = r.ID
	}
	return ids
}

func getProgramGroupIDs(groups []ProgramGroupDao) []int32 {
	ids := make([]int32, len(groups))
	for i, g := range groups {
		ids[i] = g.ID
	}
	return ids
}
