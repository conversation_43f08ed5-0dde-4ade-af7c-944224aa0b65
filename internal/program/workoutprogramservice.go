package program

import (
	"context"
	"exert-app-service/internal/db"
	"fmt"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
)

type WorkoutProgramService struct {
	Ctx    context.Context
	Pool   *pgxpool.Pool
	UserID int64
}

func NewWorkoutProgramService(ctx context.Context, pool *pgxpool.Pool) *WorkoutProgramService {
	return &WorkoutProgramService{
		Ctx:  ctx,
		Pool: pool,
	}
}

func (s *WorkoutProgramService) GetAll(q *db.Queries) ([]db.GetWorkoutProgramsRow, error) {
	programs, err := q.GetWorkoutPrograms(s.Ctx, pgtype.Int4{Int32: int32(s.UserID), Valid: true})
	if err != nil {
		return nil, fmt.Errorf("failed to get workout programs: %w", err)
	}

	return programs, nil
}

func (s *WorkoutProgramService) GetById(q *db.Queries, id int32) ([]db.GetWorkoutProgramRow, error) {
	program, err := q.GetWorkoutProgram(s.Ctx, db.GetWorkoutProgramParams{
		ID:     id,
		UserID: pgtype.Int4{Int32: int32(s.UserID), Valid: true},
	})
	if err != nil {
		return []db.GetWorkoutProgramRow{}, fmt.Errorf("failed to get workout program with id %d: %w", id, err)
	}

	return program, nil
}

func (s *WorkoutProgramService) Upsert(q *db.Queries, programDao WorkoutProgramDao) (db.WorkoutProgram, error) {
	if programDao.ID == 0 {
		return q.CreateProgram(s.Ctx, db.CreateProgramParams{
			Name:        programDao.Name,
			Description: pgtype.Text{String: programDao.Description, Valid: programDao.Description != ""},
			UserID:      pgtype.Int4{Int32: int32(s.UserID), Valid: true},
		})
	}

	return q.UpdateProgram(s.Ctx, db.UpdateProgramParams{
		ID:          programDao.ID,
		Name:        programDao.Name,
		Description: pgtype.Text{String: programDao.Description, Valid: programDao.Description != ""},
		UserID:      pgtype.Int4{Int32: int32(s.UserID), Valid: true},
	})
}

func (s *WorkoutProgramService) Delete(q *db.Queries, id int32) error {
	err := q.DeleteProgram(s.Ctx, db.DeleteProgramParams{
		ID:     id,
		UserID: pgtype.Int4{Int32: int32(s.UserID), Valid: true},
	})
	if err != nil {
		return fmt.Errorf("failed to delete workout program with id %d: %w", id, err)
	}

	return nil
}
