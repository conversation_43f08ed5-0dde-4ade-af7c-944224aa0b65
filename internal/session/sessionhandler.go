package session

import (
	"exert-app-service/internal/auth"
	"fmt"
	"log"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type SessionHandler struct {
	Ss *SessionService
}

func NewSessionHandler(ss *SessionService) *SessionHandler {
	return &SessionHandler{
		Ss: ss,
	}
}

func (h *SessionHandler) ListAll(c *fiber.Ctx) error {
	userID, ok := auth.GetUserID(c)
	if !ok {
		log.Println("user not authenticated: ", userID)
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User not authenticated",
		})
	}

	h.setSessionUserIDs(userID)
	sessions, err := h.Ss.GetAll()
	if err != nil {
		log.Println("error getting workout sessions: ", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to get workout sessions",
		})
	}

	return c.Status(fiber.StatusOK).JSON(sessions)
}

func (h *SessionHandler) List(c *fiber.Ctx) error {
	userID, ok := auth.GetUserID(c)
	if !ok {
		log.Println("user not authenticated: ", userID)
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User not authenticated",
		})
	}

	h.setSessionUserIDs(userID)
	id, err := h.getSessionId(c)
	if err != nil {
		log.Println("error getting session id: ", err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Failed to get session ID",
		})
	}

	session, err := h.Ss.Get(id)
	if err != nil || session.ID == 0 {
		log.Println("session not found: ", err)
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": "Session not found",
		})
	}

	return c.Status(fiber.StatusOK).JSON(session)
}

func (h *SessionHandler) Create(c *fiber.Ctx) error {
	userID, ok := auth.GetUserID(c)
	if !ok {
		log.Println("user not authenticated: ", userID)
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User not authenticated",
		})
	}

	h.setSessionUserIDs(userID)
	var newSession WorkoutSessionDao
	if err := c.BodyParser(&newSession); err != nil {
		log.Println("error decoding session: ", err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid session",
		})
	}

	createdSession, err := h.Ss.Create(newSession)
	if err != nil {
		log.Println("error creating workout session: ", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to create workout session",
		})
	}

	return c.Status(fiber.StatusOK).JSON(createdSession)
}

func (h *SessionHandler) Update(c *fiber.Ctx) error {
	userID, ok := auth.GetUserID(c)
	if !ok {
		log.Println("user not authenticated: ", userID)
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User not authenticated",
		})
	}

	h.setSessionUserIDs(userID)
	var updatedSession WorkoutSessionDao
	if err := c.BodyParser(&updatedSession); err != nil {
		log.Println("error decoding session: ", err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid session",
		})
	}

	updatedSession, err := h.Ss.Update(updatedSession)
	if err != nil {
		log.Println("error updating workout session: ", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to update workout session",
		})
	}

	return c.Status(fiber.StatusOK).JSON(updatedSession)
}

func (h *SessionHandler) Delete(c *fiber.Ctx) error {
	userID, ok := auth.GetUserID(c)
	if !ok {
		log.Println("user not authenticated: ", userID)
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User not authenticated",
		})
	}

	h.setSessionUserIDs(userID)
	id, err := h.getSessionId(c)
	if err != nil {
		log.Println("error getting session id: ", err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Failed to get session ID",
		})
	}

	err = h.Ss.Delete(id)
	if err != nil {
		log.Println("error deleting workout session: ", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to delete workout session",
		})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "Workout session deleted",
	})
}

func (h *SessionHandler) getSessionId(c *fiber.Ctx) (int32, error) {
	id := c.Params("sessionId")
	if id == "" {
		log.Println("no session ID provided")
		return 0, fmt.Errorf("no session ID provided")
	}

	session_id, err := strconv.Atoi(id)
	if err != nil {
		log.Println("failed to parse session ID: ", err)
		return 0, fmt.Errorf("failed to parse session ID: %w", err)
	}

	return int32(session_id), nil
}

func (h *SessionHandler) setSessionUserIDs(userID int64) {
	h.Ss.Wss.UserID = userID
	h.Ss.Sgs.UserID = userID
	h.Ss.Ss.UserID = userID
}
