package session

import (
	"context"
	"exert-app-service/internal/db"
	"fmt"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
)

type SessionSetService struct {
	Pool   *pgxpool.Pool
	Ctx    context.Context
	UserID int64
}

func NewSessionSetService(pool *pgxpool.Pool, ctx context.Context) *SessionSetService {
	return &SessionSetService{
		Pool: pool,
		Ctx:  ctx,
	}
}

func (s *SessionSetService) Upsert(q *db.Queries, sessionSetDao SessionSetDao, groupID int32) (db.SessionSet, error) {
	if sessionSetDao.ID == 0 {
		return q.CreateSessionSet(s.Ctx, db.CreateSessionSetParams{
			Reps:     sessionSetDao.Reps,
			Weight:   sessionSetDao.Weight,
			SetOrder: sessionSetDao.Order,
			GroupID:  groupID,
			UserID:   pgtype.Int4{Int32: int32(s.User<PERSON>), Valid: true},
		})
	} else {
		return q.UpdateSessionSet(s.Ctx, db.UpdateSessionSetParams{
			ID:       sessionSetDao.ID,
			Reps:     sessionSetDao.Reps,
			Weight:   sessionSetDao.Weight,
			SetOrder: sessionSetDao.Order,
			UserID:   pgtype.Int4{Int32: int32(s.UserID), Valid: true},
		})
	}
}

func (s *SessionSetService) DeleteNotInSlice(q *db.Queries, groupID int32, setIDs []int32) error {
	err := q.DeleteSessionSetsNotInSlice(s.Ctx, db.DeleteSessionSetsNotInSliceParams{
		GroupID: groupID,
		UserID:  pgtype.Int4{Int32: int32(s.UserID), Valid: true},
		Column3: setIDs,
	})
	if err != nil {
		return fmt.Errorf("failed to delete session sets: %w", err)
	}

	return nil
}
