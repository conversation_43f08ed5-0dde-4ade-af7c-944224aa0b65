package util

import (
	"encoding/json"

	"github.com/gofiber/fiber/v2"
)

type ErrorResponse struct {
	Error string `json:"error"`
}

func RespondWithError(c *fiber.Ctx, code int, message string) {
	RespondWithJSON(c, code, ErrorResponse{Error: message})
}

func RespondWithJSON(c *fiber.Ctx, code int, payload interface{}) {
	response, err := json.Marshal(payload)
	if err != nil {
		c.Status(fiber.StatusInternalServerError)
		c.Write([]byte(`{"error":"Failed to marshal JSON response"}`))
		return
	}

	c.Set("Content-Type", "application/json")
	c.Status(code)
	c.Write(response)
}
