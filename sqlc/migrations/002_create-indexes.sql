-- +goose Up

-- Indexes for program_groups
CREATE INDEX IF NOT EXISTS idx_routine_id ON program_groups (routine_id);
CREATE INDEX IF NOT EXISTS idx_deleted_at_program_groups ON program_groups (deleted_at);

-- Indexes for program_routines
CREATE INDEX IF NOT EXISTS idx_program_id ON program_routines (program_id);
CREATE INDEX IF NOT EXISTS idx_deleted_at_program_routines ON program_routines (deleted_at);

-- Indexes for session_groups
CREATE INDEX IF NOT EXISTS idx_session_id ON session_groups (session_id);
CREATE INDEX IF NOT EXISTS idx_deleted_at_session_groups ON session_groups (deleted_at);

-- Indexes for session_sets
CREATE INDEX IF NOT EXISTS idx_group_id ON session_sets (group_id);
CREATE INDEX IF NOT EXISTS idx_deleted_at_session_sets ON session_sets (deleted_at);

-- Indexes for workout_programs
CREATE INDEX IF NOT EXISTS idx_deleted_at_workout_programs ON workout_programs (deleted_at);

-- +goose Down
DROP INDEX IF EXISTS idx_routine_id;
DROP INDEX IF EXISTS idx_deleted_at_program_groups;
DROP INDEX IF EXISTS idx_program_id;
DROP INDEX IF EXISTS idx_deleted_at_program_routines;
DROP INDEX IF EXISTS idx_session_id;
DROP INDEX IF EXISTS idx_deleted_at_session_groups;
DROP INDEX IF EXISTS idx_group_id;
DROP INDEX IF EXISTS idx_deleted_at_session_sets;
DROP INDEX IF EXISTS idx_deleted_at_workout_programs;
