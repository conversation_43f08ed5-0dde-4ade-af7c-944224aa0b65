-- name: GetWorkoutPrograms :many
select wp.id,
       wp.name,
       wp.description,
       pr.id          as routine_id,
       pr.name        as routine_name,
       pr.description as routine_description,
       pr.routine_order,
       pr.program_id  as routine_program_id,
       pg.id          as group_id,
       pg.name        as group_name,
       pg.sets,
       pg.reps,
       pg.group_order,
       pg.routine_id  as group_routine_id
from workout_programs as wp
         inner join program_routines as pr on
    wp.id = pr.program_id
         inner join program_groups as pg on
    pr.id = pg.routine_id
where wp.user_id = $1;


-- name: GetWorkoutProgram :many
select wp.id,
       wp.name,
       wp.description,
       pr.id          as routine_id,
       pr.name        as routine_name,
       pr.description as routine_description,
       pr.routine_order,
       pr.program_id  as routine_program_id,
       pg.id          as group_id,
       pg.name        as group_name,
       pg.sets,
       pg.reps,
       pg.group_order,
       pg.routine_id  as group_routine_id
from workout_programs as wp
         inner join program_routines as pr on
    wp.id = pr.program_id
         inner join program_groups as pg on
    pr.id = pg.routine_id
where wp.id = $1 AND wp.user_id = $2;

-- name: GetProgramRoutine :many
select *
from program_routines as pr
where pr.id = $1 AND pr.user_id = $2;

-- name: GetProgramGroup :many
select *
from program_groups as pg
where pg.id = $1 AND pg.user_id = $2;

-- name: CreateProgram :one
INSERT INTO workout_programs (name,
                              description,
                              user_id)
VALUES ($1,
        $2,
        $3)
RETURNING *;

-- name: CreateProgramRoutine :one
INSERT INTO program_routines (name,
                              description,
                              routine_order,
                              program_id,
                              user_id)
VALUES ($1,
        $2,
        $3,
        $4,
        $5)
RETURNING *;

-- name: CreateProgramGroup :one
INSERT INTO program_groups (name,
                            sets,
                            reps,
                            group_order,
                            routine_id,
                            user_id)
VALUES ($1,
        $2,
        $3,
        $4,
        $5,
        $6)
RETURNING *;

-- name: UpdateProgram :one
UPDATE workout_programs
SET name         = $1,
    description  = $2
WHERE id = $3 AND user_id = $4
RETURNING *;

-- name: UpdateProgramRoutine :one
UPDATE program_routines
SET name          = $1,
    description   = $2,
    routine_order = $3
WHERE id = $4 AND user_id = $5
RETURNING *;

-- name: UpdateProgramGroup :one
UPDATE program_groups
SET name        = $1,
    sets        = $2,
    reps        = $3,
    group_order = $4
WHERE id = $5 AND user_id = $6
RETURNING *;

-- name: DeleteProgram :exec
DELETE
FROM workout_programs
WHERE id = $1 AND user_id = $2;

-- name: DeleteProgramGroupsNotInSlice :exec
DELETE
FROM program_groups AS pg
WHERE routine_id = $1
  AND user_id = $2
  AND NOT EXISTS (SELECT 1
                  FROM UNNEST($3::int[]) AS id
                  WHERE id = pg.id);

-- name: DeleteProgramRoutinesNotInSlice :exec
DELETE
FROM program_routines AS pr
WHERE program_id = $1
  AND user_id = $2
  AND NOT EXISTS (SELECT 1
                  FROM UNNEST($3::int[]) AS id
                  WHERE id = pr.id);
